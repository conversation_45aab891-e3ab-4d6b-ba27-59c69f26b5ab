namespace AssetView.Nx.Core.Constants;

/// <summary>
/// Roles padrão do sistema
/// </summary>
public static class SystemRoles
{
    /// <summary>
    /// Administrador do sistema (super admin)
    /// </summary>
    public const string SystemAdmin = "SystemAdmin";

    /// <summary>
    /// Administrador
    /// </summary>
    public const string Admin = "Admin";

    /// <summary>
    /// Gerente de projetos
    /// </summary>
    public const string ProjectManager = "ProjectManager";

    /// <summary>
    /// Usuário padrão
    /// </summary>
    public const string User = "User";

    /// <summary>
    /// Usuário somente leitura
    /// </summary>
    public const string ReadOnlyUser = "ReadOnlyUser";

    /// <summary>
    /// Lista de todos os roles do sistema
    /// </summary>
    public static readonly string[] AllRoles =
    {
        SystemAdmin,
        Admin,
        ProjectManager,
        User,
        ReadOnlyUser
    };

    /// <summary>
    /// Roles que podem gerenciar usuários
    /// </summary>
    public static readonly string[] UserManagementRoles =
    {
        SystemAdmin,
        Admin
    };

    /// <summary>
    /// Roles que podem gerenciar projetos
    /// </summary>
    public static readonly string[] ProjectManagementRoles =
    {
        SystemAdmin,
        Admin,
        ProjectManager
    };
}
