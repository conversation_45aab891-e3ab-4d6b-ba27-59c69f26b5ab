using AssetView.Nx.Core.Common;

namespace AssetView.Nx.Core.Entities;

/// <summary>
/// Role da aplicação
/// </summary>
public class ApplicationRole : BaseEntity
{
    /// <summary>
    /// Nome do role
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Nome normalizado do role
    /// </summary>
    public string NormalizedName { get; set; } = string.Empty;

    /// <summary>
    /// Descrição do role
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Indica se o role está ativo
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Construtor padrão
    /// </summary>
    public ApplicationRole()
    {
    }

    /// <summary>
    /// Construtor com nome do role
    /// </summary>
    public ApplicationRole(string roleName) : this()
    {
        Name = roleName;
        NormalizedName = roleName.ToUpperInvariant();
    }
}
