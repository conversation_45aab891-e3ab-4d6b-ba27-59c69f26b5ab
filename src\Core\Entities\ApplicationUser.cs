using AssetView.Nx.Core.Common;

namespace AssetView.Nx.Core.Entities;

/// <summary>
/// Usuário da aplicação
/// </summary>
public class ApplicationUser : BaseEntity
{
    /// <summary>
    /// Nome completo do usuário
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Email do usuário
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Nome de usuário
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// Hash da senha
    /// </summary>
    public string PasswordHash { get; set; } = string.Empty;

    /// <summary>
    /// Telefone do usuário
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Indica se o email foi confirmado
    /// </summary>
    public bool EmailConfirmed { get; set; } = false;

    /// <summary>
    /// Indica se o telefone foi confirmado
    /// </summary>
    public bool PhoneNumberConfirmed { get; set; } = false;

    /// <summary>
    /// Indica se o 2FA está habilitado
    /// </summary>
    public bool TwoFactorEnabled { get; set; } = false;

    /// <summary>
    /// Data de fim do bloqueio (lockout)
    /// </summary>
    public DateTime? LockoutEnd { get; set; }

    /// <summary>
    /// Indica se o lockout está habilitado
    /// </summary>
    public bool LockoutEnabled { get; set; } = false;

    /// <summary>
    /// Contador de tentativas de login falhadas
    /// </summary>
    public int AccessFailedCount { get; set; } = 0;

    /// <summary>
    /// Indica se o usuário está ativo
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Data do último login
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// Avatar/foto do usuário (URL ou base64)
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// Configurações específicas do usuário em JSON
    /// </summary>
    public string? Settings { get; set; }


}
