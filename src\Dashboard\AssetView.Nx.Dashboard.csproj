<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>


  <ItemGroup>
    <PackageReference Include="Extensions.MudBlazor.StaticInput" Version="3.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
    <PackageReference Include="Microsoft.Net.Http.Headers" Version="9.0.6" />
    <PackageReference Include="MudBlazor" Version="8.9.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\Core\AssetView.Nx.Core.csproj" />
    <ProjectReference Include="..\Infrastructure\AssetView.Nx.Infrastructure.csproj" />
    <ProjectReference Include="..\Modules\Auth\AssetView.Nx.Modules.Auth.csproj" />
    <ProjectReference Include="..\Modules\OpcDa\AssetView.Nx.Modules.OpcDa.csproj" />
    <ProjectReference Include="..\Modules\Projects\AssetView.Nx.Modules.Projects.csproj" />
    <ProjectReference Include="..\Modules\Users\AssetView.Nx.Modules.Users.csproj" />
    <ProjectReference Include="..\Shared\AssetView.Nx.Shared.csproj" />
  </ItemGroup>
</Project>