@inherits LayoutComponentBase
@using AssetView.Nx.Dashboard.Services
@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject ISnackbar Snackbar

<style>
    html, body, #app {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    .mud-main-content {
        /* garante que o MainContent ocupe todo o espaço restante */
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden; /* evita scroll fora do seu controle */
    }
</style>

<MudThemeProvider Theme="@_theme" IsDarkMode="_isDarkMode" />
<MudThemeProvider />
<MudPopoverProvider />
<MudDialogProvider />
<MudSnackbarProvider />
<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())" />
        <MudSpacer />
        <MudText Typo="Typo.h6">Smar AssetView Nx</MudText>
        <MudSpacer />
        <MudIconButton Icon="@Icons.Material.Filled.Brightness4" Color="Color.Inherit" OnClick="@((e) => DarkModeToggle())" />
        <MudMenu Icon="@Icons.Material.Filled.AccountCircle" Dense="true" Color="Color.Inherit" direction="Direction.Bottom" offset-x>
            <MudMenuItem Icon="@Icons.Material.Filled.Person">Perfil</MudMenuItem>
            <MudMenuItem Icon="@Icons.Material.Filled.Settings">Configurações</MudMenuItem>
            <MudDivider />
            <MudMenuItem Icon="@Icons.Material.Filled.Logout" OnClick="HandleLogout">Sair</MudMenuItem>
        </MudMenu>
    </MudAppBar>
    <MudDrawer id="nav-drawer" @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
        <MudDrawerHeader>
            <MudText Typo="Typo.h6">Menu</MudText>
        </MudDrawerHeader>
        <NavMenu />
    </MudDrawer>
    <MudMainContent Class="d-flex flex-column pt-16 pa-4">
        @Body
    </MudMainContent>
</MudLayout>


<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

@code {
    private bool _drawerOpen = true;
    private bool _isDarkMode = true;
    private MudTheme? _theme = null;

    protected override void OnInitialized()
    {
        base.OnInitialized();

        _theme = new()
        {
            PaletteLight = _lightPalette,
            PaletteDark = _darkPalette,
            LayoutProperties = new LayoutProperties()
        };
    }

    private void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }

    private void DarkModeToggle()
    {
        _isDarkMode = !_isDarkMode;
        //await JSRuntime.InvokeVoidAsync("mudBlazor.toggleDarkMode", _isDarkMode);
    }

    private readonly PaletteLight _lightPalette = new()
    {
        Black = "#110e2d",
        AppbarText = "#424242",
        AppbarBackground = "rgba(255,255,255,0.8)",
        DrawerBackground = "#ffffff",
        GrayLight = "#e8e8e8",
        GrayLighter = "#f9f9f9",
    };

    private readonly PaletteDark _darkPalette = new()
    {
        Primary = "#7e6fff",
        Surface = "#1e1e2d",
        Background = "#1a1a27",
        BackgroundGray = "#151521",
        AppbarText = "#92929f",
        AppbarBackground = "rgba(26,26,39,0.8)",
        DrawerBackground = "#1a1a27",
        ActionDefault = "#74718e",
        ActionDisabled = "#9999994d",
        ActionDisabledBackground = "#605f6d4d",
        TextPrimary = "#b2b0bf",
        TextSecondary = "#92929f",
        TextDisabled = "#ffffff33",
        DrawerIcon = "#92929f",
        DrawerText = "#92929f",
        GrayLight = "#2a2833",
        GrayLighter = "#1e1e2d",
        Info = "#4a86ff",
        Success = "#3dcb6c",
        Warning = "#ffb545",
        Error = "#ff3f5f",
        LinesDefault = "#33323e",
        TableLines = "#33323e",
        Divider = "#292838",
        OverlayLight = "#1e1e2d80",
    };

    public string DarkLightModeButtonIcon => _isDarkMode switch
    {
        true => Icons.Material.Rounded.AutoMode,
        false => Icons.Material.Outlined.DarkMode,
    };

    private async Task HandleLogout()
    {
        try
        {
            var customAuthProvider = (CustomAuthenticationStateProvider)AuthStateProvider;
            var result = await customAuthProvider.LogoutAsync();

            if (result.Success)
            {
                Snackbar.Add("Logout realizado com sucesso!", Severity.Success);
                Navigation.NavigateTo("/login", true);
            }
            else
            {
                Snackbar.Add(result.Message ?? "Erro ao fazer logout", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Erro interno. Tente novamente mais tarde.", Severity.Error);
        }
    }
}


