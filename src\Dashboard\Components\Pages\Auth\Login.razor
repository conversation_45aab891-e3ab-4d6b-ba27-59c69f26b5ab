@page "/login"
@layout AuthLayout
@using MudBlazor
@using AssetView.Nx.Dashboard.Services
@using Microsoft.AspNetCore.WebUtilities
@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthStateProvider
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Login - Smar AssetView Nx</PageTitle>

<MudContainer MaxWidth="MaxWidth.Small" Class="mt-16">
    <MudPaper Elevation="4" Class="pa-8">
        <MudStack Spacing="4">
            <!-- Header -->
            <MudStack AlignItems="AlignItems.Center" Spacing="2">
                <MudIcon Icon="@Icons.Material.Filled.AccountCircle" Size="Size.Large" Color="Color.Primary" />
                <MudText Typo="Typo.h4" Align="Align.Center" Color="Color.Primary">
                    Smar AssetView Nx
                </MudText>
                <MudText Typo="Typo.body1" Align="Align.Center" Color="Color.Secondary">
                    Faça login para continuar
                </MudText>
            </MudStack>

            <!-- Formulário -->
            <EditForm Model="@_model" OnValidSubmit="@HandleSubmit">
                <DataAnnotationsValidator />

                <MudStack Spacing="3">
                    <MudTextField @bind-Value="_model.EmailOrUserName" Margin="Margin.Dense"
                                  Label="E-mail ou Nome de Usuário"
                                  Variant="Variant.Outlined"
                                  Adornment="Adornment.Start"
                                  AdornmentIcon="@Icons.Material.Filled.Person"
                                  Required="true"
                                  RequiredError="E-mail ou nome de usuário é obrigatório"
                                  For="@(() => _model.EmailOrUserName)"
                                  Disabled="@_isLoading" />

                    <MudTextField @bind-Value="_model.Password" Margin="Margin.Dense"
                                  Label="Senha"
                                  Variant="Variant.Outlined"
                                  InputType="@(_showPassword ? InputType.Text : InputType.Password)"
                                  Adornment="Adornment.End"
                                  AdornmentIcon="@(_showPassword ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)"
                                  OnAdornmentClick="@(() => _showPassword = !_showPassword)"
                                  Required="true"
                                  RequiredError="Senha é obrigatória"
                                  For="@(() => _model.Password)"
                                  Disabled="@_isLoading" />

                    <MudCheckBox T="bool" @bind-checked="_model.RememberMe" Dense="true"
                                 Label="Lembrar-me"
                                 Color="Color.Primary"
                                 Disabled="@_isLoading" />

                    <MudButton ButtonType="ButtonType.Submit"
                               Variant="Variant.Filled"
                               Color="Color.Primary"
                               Size="Size.Large"
                               FullWidth="true"
                               StartIcon="@Icons.Material.Filled.Login"
                               loading="@_isLoading"
                               loading-text="Entrando...">
                        Entrar
                    </MudButton>
                </MudStack>
            </EditForm>

            <!-- Link de recuperação de senha -->
            <MudStack AlignItems="AlignItems.Center">
                <MudButton Href="/forgot-password"
                           Variant="Variant.Text"
                           Color="Color.Primary"
                           Size="Size.Small"
                           StartIcon="@Icons.Material.Filled.Lock">
                    Esqueceu sua senha?
                </MudButton>
            </MudStack>


        </MudStack>
    </MudPaper>
</MudContainer>

@code {
    private readonly LoginModel _model = new();
    private bool _isLoading = false;
    private bool _showPassword = false;

    protected override void OnInitialized()
    {
        // Verificar se há mensagem de erro na URL
        var uri = new Uri(Navigation.Uri);
        if (QueryHelpers.ParseQuery(uri.Query).TryGetValue("error", out var error))
        {
            Snackbar.Add(error.ToString(), Severity.Error);
        }
    }

    private async Task HandleSubmit()
    {
        if (_isLoading) return;

        _isLoading = true;
        StateHasChanged();

        try
        {
            var customAuthProvider = (CustomAuthenticationStateProvider)AuthStateProvider;
            var result = await customAuthProvider.LoginAsync(_model.EmailOrUserName, _model.Password, _model.RememberMe);

            if (result.Success)
            {
                Snackbar.Add("Login realizado com sucesso!", Severity.Success);

                // Aguardar um pouco para garantir que o estado foi atualizado
                await Task.Delay(100);

                // Obter o estado de autenticação atualizado para verificar os roles
                var authState = await AuthStateProvider.GetAuthenticationStateAsync();
                var user = authState.User;

                // Determinar a página de redirecionamento baseada no perfil do usuário
                string redirectUrl = "/opc/monitor"; // Padrão para usuários não-admin

                if (user.IsInRole("SuperAdmin") || user.IsInRole("Admin"))
                {
                    redirectUrl = "/configurations"; // Admins vão para listar projetos
                }

                // Navegar sem forçar reload para permitir que o estado seja mantido
                Navigation.NavigateTo(redirectUrl, false);
            }
            else
            {
                Snackbar.Add(result.Message ?? "Erro ao fazer login", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Erro interno. Tente novamente mais tarde.", Severity.Error);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }



    public class LoginModel
    {
        [Required(ErrorMessage = "E-mail ou nome de usuário é obrigatório")]
        public string EmailOrUserName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Senha é obrigatória")]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;
    }
}
