@page "/"
@inherits ProtectedPageBase
@using AssetView.Nx.Dashboard.Components.Shared

<PageTitle>Dashboard - Smar AssetView Nx</PageTitle>

@code {
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        // Redirecionar baseado no perfil do usuário
        if (IsAdmin)
        {
            Navigation.NavigateTo("/configurations", true);
        }
        else
        {
            Navigation.NavigateTo("/opc/monitor", true);
        }
    }
}

<!-- Página de redirecionamento automático baseada no perfil do usuário -->
<div class="d-flex justify-center align-center" style="min-height: 50vh;">
    <MudProgressCircular Indeterminate="true" />
    <MudText Class="ml-4">Redirecionando...</MudText>
</div>
