@page "/profile"
@inherits ProtectedPageBase
@inject IUserManagementService UserService
@inject ISnackbar Snackbar
@using AssetView.Nx.Modules.Users.Services
@using AssetView.Nx.Core.Entities
@using AssetView.Nx.Dashboard.Components.Shared
@using System.ComponentModel.DataAnnotations

<PageTitle>Meu Perfil - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudText Typo="Typo.h4" Class="mb-4">Meu Perfil</MudText>

    @if (_loading)
    {
        <div class="d-flex justify-center pa-4">
            <MudProgressCircular Indeterminate="true" />
        </div>
    }
    else
    {
        <MudGrid>
            <!-- Informações do Perfil -->
            <MudItem xs="12" md="8">
                <MudCard Elevation="2" Class="mb-4">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Informações Pessoais</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <EditForm Model="_profileModel" OnValidSubmit="SaveProfile">
                            <DataAnnotationsValidator />
                            
                            <MudGrid>
                                <MudItem xs="12" md="6">
                                    <MudTextField @bind-Value="_profileModel.FullName"
                                                  Label="Nome Completo"
                                                  Variant="Variant.Outlined"
                                                  Required="true"
                                                  RequiredError="Nome completo é obrigatório"
                                                  For="@(() => _profileModel.FullName)"
                                                  Disabled="@_savingProfile" />
                                </MudItem>
                                
                                <MudItem xs="12" md="6">
                                    <MudTextField @bind-Value="_profileModel.UserName"
                                                  Label="Nome de Usuário"
                                                  Variant="Variant.Outlined"
                                                  Required="true"
                                                  RequiredError="Nome de usuário é obrigatório"
                                                  For="@(() => _profileModel.UserName)"
                                                  Disabled="@_savingProfile" />
                                </MudItem>
                                
                                <MudItem xs="12" md="6">
                                    <MudTextField @bind-Value="_profileModel.Email"
                                                  Label="E-mail"
                                                  Variant="Variant.Outlined"
                                                  InputType="InputType.Email"
                                                  Required="true"
                                                  RequiredError="E-mail é obrigatório"
                                                  For="@(() => _profileModel.Email)"
                                                  Disabled="@_savingProfile" />
                                </MudItem>
                                
                                <MudItem xs="12" md="6">
                                    <MudTextField @bind-Value="_profileModel.PhoneNumber"
                                                  Label="Telefone"
                                                  Variant="Variant.Outlined"
                                                  InputType="InputType.Telephone"
                                                  For="@(() => _profileModel.PhoneNumber)"
                                                  Disabled="@_savingProfile" />
                                </MudItem>
                            </MudGrid>
                            
                            <div class="d-flex justify-end mt-4">
                                <MudButton Variant="Variant.Filled" 
                                           Color="Color.Primary" 
                                           ButtonType="ButtonType.Submit"
                                           Disabled="@_savingProfile"
                                           StartIcon="@Icons.Material.Filled.Save">
                                    @if (_savingProfile)
                                    {
                                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                        <MudText Class="ms-2">Salvando...</MudText>
                                    }
                                    else
                                    {
                                        <MudText>Salvar Perfil</MudText>
                                    }
                                </MudButton>
                            </div>
                        </EditForm>
                    </MudCardContent>
                </MudCard>

                <!-- Alterar Senha -->
                <MudCard Elevation="2">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Alterar Senha</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <EditForm Model="_passwordModel" OnValidSubmit="ChangePassword">
                            <DataAnnotationsValidator />
                            
                            <MudGrid>
                                <MudItem xs="12">
                                    <MudTextField @bind-Value="_passwordModel.CurrentPassword"
                                                  Label="Senha Atual"
                                                  Variant="Variant.Outlined"
                                                  InputType="@(_showCurrentPassword ? InputType.Text : InputType.Password)"
                                                  Required="true"
                                                  RequiredError="Senha atual é obrigatória"
                                                  For="@(() => _passwordModel.CurrentPassword)"
                                                  Disabled="@_changingPassword"
                                                  Adornment="Adornment.End"
                                                  AdornmentIcon="@(_showCurrentPassword ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)"
                                                  OnAdornmentClick="ToggleCurrentPasswordVisibility" />
                                </MudItem>
                                
                                <MudItem xs="12" md="6">
                                    <MudTextField @bind-Value="_passwordModel.NewPassword"
                                                  Label="Nova Senha"
                                                  Variant="Variant.Outlined"
                                                  InputType="@(_showNewPassword ? InputType.Text : InputType.Password)"
                                                  Required="true"
                                                  RequiredError="Nova senha é obrigatória"
                                                  For="@(() => _passwordModel.NewPassword)"
                                                  Disabled="@_changingPassword"
                                                  Adornment="Adornment.End"
                                                  AdornmentIcon="@(_showNewPassword ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)"
                                                  OnAdornmentClick="ToggleNewPasswordVisibility" />
                                </MudItem>
                                
                                <MudItem xs="12" md="6">
                                    <MudTextField @bind-Value="_passwordModel.ConfirmPassword"
                                                  Label="Confirmar Nova Senha"
                                                  Variant="Variant.Outlined"
                                                  InputType="@(_showConfirmPassword ? InputType.Text : InputType.Password)"
                                                  Required="true"
                                                  RequiredError="Confirmação de senha é obrigatória"
                                                  For="@(() => _passwordModel.ConfirmPassword)"
                                                  Disabled="@_changingPassword"
                                                  Adornment="Adornment.End"
                                                  AdornmentIcon="@(_showConfirmPassword ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)"
                                                  OnAdornmentClick="ToggleConfirmPasswordVisibility" />
                                </MudItem>
                            </MudGrid>
                            
                            <div class="d-flex justify-end mt-4">
                                <MudButton Variant="Variant.Filled" 
                                           Color="Color.Secondary" 
                                           ButtonType="ButtonType.Submit"
                                           Disabled="@_changingPassword"
                                           StartIcon="@Icons.Material.Filled.Lock">
                                    @if (_changingPassword)
                                    {
                                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                        <MudText Class="ms-2">Alterando...</MudText>
                                    }
                                    else
                                    {
                                        <MudText>Alterar Senha</MudText>
                                    }
                                </MudButton>
                            </div>
                        </EditForm>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- Informações da Conta -->
            <MudItem xs="12" md="4">
                <MudCard Elevation="2" Class="mb-4">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Informações da Conta</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (_currentUser != null)
                        {
                            <MudStack Spacing="3">
                                <div class="d-flex justify-center mb-4">
                                    <MudAvatar Color="Color.Primary" Size="Size.Large">
                                        @_currentUser.FullName.Substring(0, 1).ToUpper()
                                    </MudAvatar>
                                </div>
                                
                                <div class="d-flex justify-space-between">
                                    <MudText Typo="Typo.body2">Status:</MudText>
                                    <MudChip T="string" Color="@(_currentUser.IsActive ? Color.Success : Color.Error)" Size="Size.Small">
                                        @(_currentUser.IsActive ? "Ativo" : "Inativo")
                                    </MudChip>
                                </div>
                                
                                <div class="d-flex justify-space-between">
                                    <MudText Typo="Typo.body2">Email Confirmado:</MudText>
                                    <MudIcon Icon="@(_currentUser.EmailConfirmed ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)" 
                                            Color="@(_currentUser.EmailConfirmed ? Color.Success : Color.Error)" />
                                </div>
                                
                                <div class="d-flex justify-space-between">
                                    <MudText Typo="Typo.body2">2FA Habilitado:</MudText>
                                    <MudIcon Icon="@(_currentUser.TwoFactorEnabled ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)" 
                                            Color="@(_currentUser.TwoFactorEnabled ? Color.Success : Color.Error)" />
                                </div>
                                
                                <MudDivider />
                                
                                <div>
                                    <MudText Typo="Typo.body2"><strong>Último Login:</strong></MudText>
                                    <MudText Typo="Typo.body2">@(_currentUser.LastLoginAt?.ToString("dd/MM/yyyy HH:mm") ?? "Nunca")</MudText>
                                </div>
                                
                                <div>
                                    <MudText Typo="Typo.body2"><strong>Conta Criada:</strong></MudText>
                                    <MudText Typo="Typo.body2">@_currentUser.CreatedAt.ToString("dd/MM/yyyy HH:mm")</MudText>
                                </div>
                            </MudStack>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    }
</MudContainer>

@code {
    private bool _loading = true;
    private bool _savingProfile = false;
    private bool _changingPassword = false;
    private bool _showCurrentPassword = false;
    private bool _showNewPassword = false;
    private bool _showConfirmPassword = false;
    
    private ApplicationUser? _currentUser;
    private UserProfileModel _profileModel = new();
    private ChangePasswordModel _passwordModel = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadUserProfile();
    }

    private async Task LoadUserProfile()
    {
        _loading = true;
        try
        {
            // Por simplicidade, vamos simular um usuário atual
            // Em uma implementação real, você obteria o ID do usuário atual do contexto de autenticação
            var users = await UserService.GetUsersAsync();
            _currentUser = users.FirstOrDefault();

            if (_currentUser != null)
            {
                _profileModel.FullName = _currentUser.FullName;
                _profileModel.UserName = _currentUser.UserName;
                _profileModel.Email = _currentUser.Email;
                _profileModel.PhoneNumber = _currentUser.PhoneNumber;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar perfil: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task SaveProfile()
    {
        if (_savingProfile || _currentUser == null) return;

        _savingProfile = true;
        try
        {
            var updateRequest = new UpdateUserRequest
            {
                FullName = _profileModel.FullName,
                UserName = _profileModel.UserName,
                Email = _profileModel.Email,
                PhoneNumber = _profileModel.PhoneNumber,
                IsActive = _currentUser.IsActive,
                RoleIds = new List<string>() // Manter roles existentes
            };

            var result = await UserService.UpdateUserAsync(_currentUser.Id, updateRequest);
            if (result.Success)
            {
                Snackbar.Add(result.Message ?? "Perfil atualizado com sucesso!", Severity.Success);
                await LoadUserProfile(); // Recarregar dados
            }
            else
            {
                if (result.Errors.Any())
                {
                    foreach (var error in result.Errors)
                    {
                        Snackbar.Add(error, Severity.Error);
                    }
                }
                else
                {
                    Snackbar.Add(result.Message ?? "Erro ao atualizar perfil", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar perfil: {ex.Message}", Severity.Error);
        }
        finally
        {
            _savingProfile = false;
        }
    }

    private async Task ChangePassword()
    {
        if (_changingPassword) return;

        _changingPassword = true;
        try
        {
            // Implementar mudança de senha
            // Por simplicidade, apenas mostramos uma mensagem
            await Task.Delay(1000); // Simular processamento

            Snackbar.Add("Senha alterada com sucesso!", Severity.Success);

            // Limpar formulário
            _passwordModel = new ChangePasswordModel();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao alterar senha: {ex.Message}", Severity.Error);
        }
        finally
        {
            _changingPassword = false;
        }
    }

    private void ToggleCurrentPasswordVisibility()
    {
        _showCurrentPassword = !_showCurrentPassword;
    }

    private void ToggleNewPasswordVisibility()
    {
        _showNewPassword = !_showNewPassword;
    }

    private void ToggleConfirmPasswordVisibility()
    {
        _showConfirmPassword = !_showConfirmPassword;
    }

    public class UserProfileModel
    {
        [Required(ErrorMessage = "Nome completo é obrigatório")]
        [StringLength(200, ErrorMessage = "Nome completo deve ter no máximo 200 caracteres")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Nome de usuário é obrigatório")]
        [StringLength(256, ErrorMessage = "Nome de usuário deve ter no máximo 256 caracteres")]
        public string UserName { get; set; } = string.Empty;

        [Required(ErrorMessage = "E-mail é obrigatório")]
        [EmailAddress(ErrorMessage = "E-mail inválido")]
        [StringLength(256, ErrorMessage = "E-mail deve ter no máximo 256 caracteres")]
        public string Email { get; set; } = string.Empty;

        [Phone(ErrorMessage = "Telefone inválido")]
        [StringLength(50, ErrorMessage = "Telefone deve ter no máximo 50 caracteres")]
        public string? PhoneNumber { get; set; }
    }

    public class ChangePasswordModel
    {
        [Required(ErrorMessage = "Senha atual é obrigatória")]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "Nova senha é obrigatória")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Nova senha deve ter entre 6 e 100 caracteres")]
        public string NewPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "Confirmação de senha é obrigatória")]
        [Compare(nameof(NewPassword), ErrorMessage = "Senhas não coincidem")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}
