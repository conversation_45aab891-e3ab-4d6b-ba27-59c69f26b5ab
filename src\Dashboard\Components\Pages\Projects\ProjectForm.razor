@page "/configurations/create"
@page "/configurations/edit/{ProjectId}"
@inherits ProtectedPageBase
@using AssetView.Nx.Dashboard.Components.Shared
@inject IProjectService ProjectService
@inject ISnackbar Snackbar

<PageTitle>@(_isEdit ? "Editar Configuração" : "Nova Configuração") - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <div class="d-flex align-center mb-4">
        <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" OnClick="GoBack" />
        <MudText Typo="Typo.h4" Class="ml-2">@(_isEdit ? "Editar Configuração" : "Nova Configuração")</MudText>
    </div>

    <MudCard Elevation="2">
        <MudCardContent>
            <EditForm Model="_model" OnValidSubmit="SaveProject">
                <DataAnnotationsValidator />
                
                <MudGrid>
                    <MudItem xs="12">
                        <MudTextField @bind-Value="_model.Name"
                                     Label="Nome da Configuração"
                                     Variant="Variant.Outlined"
                                     Required="true"
                                     For="@(() => _model.Name)" />
                    </MudItem>

                    <MudItem xs="12">
                        <MudTextField @bind-Value="_model.Description"
                                     Label="Descrição"
                                     Variant="Variant.Outlined"
                                     Lines="3"
                                     For="@(() => _model.Description)" />
                    </MudItem>
                </MudGrid>
                
                <div class="d-flex justify-end gap-2 mt-4">
                    <MudButton Variant="Variant.Text" OnClick="GoBack">
                        Cancelar
                    </MudButton>
                    <MudButton ButtonType="ButtonType.Submit" 
                              Variant="Variant.Filled" 
                              Color="Color.Primary" 
                              Disabled="_saving">
                        @if (_saving)
                        {
                            <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                            <MudText Class="ms-2">Salvando...</MudText>
                        }
                        else
                        {
                            <MudText>@(_isEdit ? "Atualizar" : "Criar") Configuração</MudText>
                        }
                    </MudButton>
                </div>
            </EditForm>
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    [Parameter] public string? ProjectId { get; set; }

    private bool _isEdit => !string.IsNullOrEmpty(ProjectId);
    private bool _saving = false;
    private ProjectFormModel _model = new();

    protected override async Task OnInitializedAsync()
    {
        if (_isEdit && !string.IsNullOrEmpty(ProjectId))
        {
            await LoadProject();
        }
    }

    private async Task LoadProject()
    {
        try
        {
            var project = await ProjectService.GetProjectByIdAsync(ProjectId!);
            if (project != null)
            {
                _model = new ProjectFormModel
                {
                    Name = project.Name,
                    Description = project.Description
                };
            }
            else
            {
                Snackbar.Add("Configuração não encontrada", Severity.Error);
                Navigation.NavigateTo("/configurations");
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar configuração: {ex.Message}", Severity.Error);
            Navigation.NavigateTo("/configurations");
        }
    }

    private async Task SaveProject()
    {
        _saving = true;
        try
        {
            if (_isEdit)
            {
                var updateRequest = new UpdateProjectRequest
                {
                    Name = _model.Name,
                    Description = _model.Description,
                    Priority = ProjectPriority.Medium,
                    Tags = new List<string>()
                };

                var result = await ProjectService.UpdateProjectAsync(ProjectId!, updateRequest);
                if (result.Success)
                {
                    Snackbar.Add("Configuração atualizada com sucesso", Severity.Success);
                    Navigation.NavigateTo("/configurations");
                }
                else
                {
                    Snackbar.Add(result.Message ?? "Erro ao atualizar configuração", Severity.Error);
                }
            }
            else
            {
                var createRequest = new CreateProjectRequest
                {
                    Name = _model.Name,
                    Description = _model.Description,
                    OwnerId = CurrentUserId ?? string.Empty,
                    Status = ProjectStatus.Planning,
                    Priority = ProjectPriority.Medium,
                    Tags = new List<string>()
                };

                var result = await ProjectService.CreateProjectAsync(createRequest);
                if (result.Success)
                {
                    Snackbar.Add("Configuração criada com sucesso", Severity.Success);
                    Navigation.NavigateTo("/configurations");
                }
                else
                {
                    Snackbar.Add(result.Message ?? "Erro ao criar configuração", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar configuração: {ex.Message}", Severity.Error);
        }
        finally
        {
            _saving = false;
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/configurations");
    }

    public class ProjectFormModel
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
    }
}
