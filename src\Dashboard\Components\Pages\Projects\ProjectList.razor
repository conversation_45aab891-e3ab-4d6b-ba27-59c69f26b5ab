@page "/configurations"
@inject IProjectService ProjectService
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<PageTitle>Configurações - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">Configurações</MudText>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add" Href="/configurations/create">
            Nova Configuração
        </MudButton>
    </div>

    <MudCard Elevation="2">
        <MudCardContent>
            <div class="d-flex gap-4 mb-4">
                <MudTextField @bind-Value="_searchTerm"
                             Label="Buscar projetos"
                             Variant="Variant.Outlined"
                             Adornment="Adornment.End"
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchProjects(); })" />

                <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="SearchProjects">
                    Buscar
                </MudButton>
            </div>

            @if (_loading)
            {
                <div class="d-flex justify-center pa-4">
                    <MudProgressCircular Indeterminate="true" />
                </div>
            }
            else
            {
                <MudTable Items="_projects" 
                         Hover="true" 
                         Breakpoint="Breakpoint.Sm" 
                         Loading="_loading"
                         LoadingProgressColor="Color.Info">
                    <HeaderContent>
                        <MudTh>Nome</MudTh>
                        <MudTh>Proprietário</MudTh>
                        <MudTh>Ações</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="Nome">
                            <div>
                                <MudText Typo="Typo.body1">@context.Name</MudText>
                                @if (!string.IsNullOrEmpty(context.Description))
                                {
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">@context.Description</MudText>
                                }
                            </div>
                        </MudTd>
                        <MudTd DataLabel="Proprietário">
                            @(context.Owner?.FullName ?? "N/A")
                        </MudTd>
                        <MudTd DataLabel="Ações">
                            <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                             Color="Color.Primary"
                                             Size="Size.Small"
                                             Href="@($"/configurations/edit/{context.Id}")" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                             Color="Color.Error" 
                                             Size="Size.Small"
                                             OnClick="@(() => DeleteProject(context))" />
                            </MudButtonGroup>
                        </MudTd>
                    </RowTemplate>
                    <NoRecordsContent>
                        <MudText Typo="Typo.body1" Align="Align.Center" Class="pa-4">
                            Nenhuma configuração encontrada
                        </MudText>
                    </NoRecordsContent>
                </MudTable>

                @if (_pagedResult.TotalPages > 1)
                {
                    <div class="d-flex justify-center mt-4">
                        <MudPagination Count="_pagedResult.TotalPages" 
                                     Selected="_currentPage" 
                                     SelectedChanged="OnPageChanged" 
                                     ShowFirstButton="true" 
                                     ShowLastButton="true" />
                    </div>
                }
            }
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private List<Project> _projects = new();
    private PagedResult<Project> _pagedResult = new();
    private bool _loading = true;
    private string _searchTerm = string.Empty;
    private int _currentPage = 1;
    private const int PageSize = 10;

    protected override async Task OnInitializedAsync()
    {
        await LoadProjects();
    }

    private async Task LoadProjects()
    {
        _loading = true;
        try
        {
            _pagedResult = await ProjectService.GetProjectsPagedAsync(_currentPage, PageSize, _searchTerm, null);
            _projects = _pagedResult.Items.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar configurações: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task SearchProjects()
    {
        _currentPage = 1;
        await LoadProjects();
    }

    private async Task OnPageChanged(int page)
    {
        _currentPage = page;
        await LoadProjects();
    }

    private async Task DeleteProject(Project project)
    {
        var parameters = new DialogParameters
        {
            ["ContentText"] = $"Tem certeza que deseja excluir a configuração '{project.Name}'?",
            ["ButtonText"] = "Excluir",
            ["Color"] = Color.Error
        };

        var dialog = await DialogService.ShowAsync<ConfirmDialog>("Confirmar Exclusão", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            try
            {
                var deleteResult = await ProjectService.DeleteProjectAsync(project.Id);
                if (deleteResult.Success)
                {
                    Snackbar.Add("Configuração excluída com sucesso", Severity.Success);
                    await LoadProjects();
                }
                else
                {
                    Snackbar.Add(deleteResult.Message ?? "Erro ao excluir configuração", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Erro ao excluir configuração: {ex.Message}", Severity.Error);
            }
        }
    }


}
