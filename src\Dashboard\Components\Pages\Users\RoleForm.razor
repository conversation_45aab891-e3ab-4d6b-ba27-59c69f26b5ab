@page "/users/roles/create"
@page "/users/roles/edit/{RoleId}"
@inherits SuperAdminPageBase
@inject IRoleManagementService RoleService
@inject ISnackbar Snackbar
@using AssetView.Nx.Modules.Users.Services
@using AssetView.Nx.Core.Entities
@using AssetView.Nx.Dashboard.Components.Shared
@using System.ComponentModel.DataAnnotations

<PageTitle>@(_isEdit ? "Editar Role" : "Nova Role") - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium">
    <div class="d-flex align-center mb-4">
        <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" OnClick="GoBack" />
        <MudText Typo="Typo.h4" Class="ml-2">@(_isEdit ? "Editar Role" : "Nova Role")</MudText>
    </div>

    <MudCard Elevation="2">
        <MudCardContent>
            <EditForm Model="_model" OnValidSubmit="SaveRole">
                <DataAnnotationsValidator />
                
                <MudGrid>
                    <!-- Informações Básicas -->
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6" Class="mb-4">Informações da Role</MudText>
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudSelect T="string" @bind-Value="_model.Name"
                                   Label="Nome da Role"
                                   Variant="Variant.Outlined"
                                   Required="true"
                                   RequiredError="Nome da role é obrigatório"
                                   For="@(() => _model.Name)"
                                   Disabled="@_saving"
                                   HelperText="Selecione o tipo de role">
                            @foreach (var roleOption in _roleOptions)
                            {
                                <MudSelectItem T="string" Value="@roleOption">@roleOption</MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="_model.Description"
                                      Label="Descrição"
                                      Variant="Variant.Outlined"
                                      Lines="3"
                                      For="@(() => _model.Description)"
                                      Disabled="@_saving"
                                      HelperText="Descrição opcional para explicar o propósito da role" />
                    </MudItem>

                    <!-- Status -->
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6" Class="mb-4 mt-4">Status</MudText>
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudSwitch @bind-Value="_model.IsActive"
                                   Label="Role Ativa"
                                   Color="Color.Primary"
                                   Disabled="@_saving" />
                    </MudItem>

                    @if (_isEdit && _currentRole != null)
                    {
                        <!-- Informações Adicionais -->
                        <MudItem xs="12">
                            <MudText Typo="Typo.h6" Class="mb-4 mt-4">Informações Adicionais</MudText>
                        </MudItem>
                        
                        <MudItem xs="12" md="6">
                            <MudTextField Label="Criado em"
                                          Value="@_currentRole.CreatedAt.ToString("dd/MM/yyyy HH:mm")"
                                          Variant="Variant.Outlined"
                                          ReadOnly="true" />
                        </MudItem>
                        
                        <MudItem xs="12" md="6">
                            <MudTextField Label="Última atualização"
                                          Value="@_currentRole.UpdatedAt.ToString("dd/MM/yyyy HH:mm")"
                                          Variant="Variant.Outlined"
                                          ReadOnly="true" />
                        </MudItem>

                        <MudItem xs="12">
                            <MudChip T="string"
                                     Color="Color.Info"
                                     Size="Size.Small"
                                     Icon="Icons.Material.Filled.Person">
                                Role Personalizada
                            </MudChip>
                        </MudItem>
                    }
                </MudGrid>
            </EditForm>
        </MudCardContent>
        
        <MudCardActions>
            <MudButton Variant="Variant.Text" OnClick="GoBack" Disabled="@_saving">
                Cancelar
            </MudButton>
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       OnClick="SaveRole"
                       Disabled="@_saving"
                       StartIcon="@Icons.Material.Filled.Save">
                @if (_saving)
                {
                    <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                    <MudText Class="ms-2">Salvando...</MudText>
                }
                else
                {
                    <MudText>@(_isEdit ? "Atualizar" : "Criar") Role</MudText>
                }
            </MudButton>
        </MudCardActions>
    </MudCard>

    @if (_isEdit)
    {
        <!-- Ações Perigosas -->
        <MudCard Elevation="2" Class="mt-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6" Color="Color.Error">Zona de Perigo</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudText Typo="Typo.body2" Class="mb-4">
                    As ações abaixo são irreversíveis. Tenha certeza antes de prosseguir.
                </MudText>
                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Error" 
                           StartIcon="@Icons.Material.Filled.Delete"
                           OnClick="DeleteRole"
                           Disabled="@_saving">
                    Excluir Role
                </MudButton>
            </MudCardContent>
        </MudCard>
    }
</MudContainer>

@code {
    [Parameter] public string? RoleId { get; set; }

    private bool _isEdit => !string.IsNullOrEmpty(RoleId);
    private bool _saving = false;
    private RoleFormModel _model = new();
    private ApplicationRole? _currentRole;

    private readonly string[] _roleOptions = new[]
    {
        "Administrador",
        "Engenharia",
        "Operador"
    };

    protected override async Task OnInitializedAsync()
    {
        if (_isEdit && !string.IsNullOrEmpty(RoleId))
        {
            await LoadRole();
        }
    }

    private async Task LoadRole()
    {
        try
        {
            _currentRole = await RoleService.GetRoleByIdAsync(RoleId!);
            if (_currentRole == null)
            {
                Snackbar.Add("Role não encontrada", Severity.Error);
                Navigation.NavigateTo("/users/roles");
                return;
            }

            _model.Name = _currentRole.Name;
            _model.Description = _currentRole.Description;
            _model.IsActive = _currentRole.IsActive;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar role: {ex.Message}", Severity.Error);
            Navigation.NavigateTo("/users/roles");
        }
    }

    private async Task SaveRole()
    {
        if (_saving) return;

        _saving = true;
        try
        {
            RoleResult result;
            
            if (_isEdit)
            {
                var updateRequest = new UpdateRoleRequest
                {
                    Name = _model.Name,
                    Description = _model.Description,
                    IsActive = _model.IsActive
                };

                result = await RoleService.UpdateRoleAsync(RoleId!, updateRequest);
            }
            else
            {
                var createRequest = new CreateRoleRequest
                {
                    Name = _model.Name,
                    Description = _model.Description,
                    IsActive = _model.IsActive
                };

                result = await RoleService.CreateRoleAsync(createRequest);
            }

            if (result.Success)
            {
                Snackbar.Add(result.Message ?? "Role salva com sucesso!", Severity.Success);
                Navigation.NavigateTo("/users/roles");
            }
            else
            {
                if (result.Errors.Any())
                {
                    foreach (var error in result.Errors)
                    {
                        Snackbar.Add(error, Severity.Error);
                    }
                }
                else
                {
                    Snackbar.Add(result.Message ?? "Erro ao salvar role", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar role: {ex.Message}", Severity.Error);
        }
        finally
        {
            _saving = false;
        }
    }

    private async Task DeleteRole()
    {
        // Implementar confirmação de exclusão
        Navigation.NavigateTo("/users/roles");
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/users/roles");
    }

    public class RoleFormModel
    {
        [Required(ErrorMessage = "Nome da role é obrigatório")]
        [StringLength(256, ErrorMessage = "Nome da role deve ter no máximo 256 caracteres")]
        public string Name { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "Descrição deve ter no máximo 500 caracteres")]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;
    }
}
