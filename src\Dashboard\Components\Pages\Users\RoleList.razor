@page "/users/roles"
@inherits SuperAdminPageBase
@inject IRoleManagementService RoleService
@inject IDialogService DialogService
@inject ISnackbar Snackbar
@using AssetView.Nx.Modules.Users.Services
@using AssetView.Nx.Core.Entities
@using AssetView.Nx.Dashboard.Components.Shared

<PageTitle>Roles e Permissões - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">Roles e Permissões</MudText>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add" Href="/users/roles/create">
            Nova Role
        </MudButton>
    </div>

    <!-- Filtros -->
    <MudCard Class="mb-4" Elevation="1">
        <MudCardContent>
            <MudGrid Justify="Justify.Center">
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="_searchTerm" Margin="Margin.Dense"
                                  Label="Buscar roles"
                                  Variant="Variant.Outlined"
                                  Adornment="Adornment.Start"
                                  AdornmentIcon="@Icons.Material.Filled.Search"
                                  OnKeyDown="@(async (e) => { if (e.Key == "Enter") await SearchRoles(); })"
                                  Clearable="true"
                                  OnClearButtonClick="@(async () => { _searchTerm = string.Empty; await SearchRoles(); })" />
                </MudItem>
                <MudItem xs="12" md="3">
                    <MudSelect T="bool?" @bind-Value="_selectedStatus" Dense="true" Label="Status" Variant="Variant.Outlined" Clearable="true">
                        <MudSelectItem T="bool?" Value="true">Ativo</MudSelectItem>
                        <MudSelectItem T="bool?" Value="false">Inativo</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="3">
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="SearchRoles" FullWidth="true">
                        Buscar
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    <!-- Lista de Roles -->
    <MudCard Elevation="2">
        <MudCardContent Class="pa-0">
            @if (_loading)
            {
                <div class="d-flex justify-center pa-4">
                    <MudProgressCircular Indeterminate="true" />
                </div>
            }
            else if (!_roles.Any())
            {
                <div class="d-flex flex-column align-center pa-8">
                    <MudIcon Icon="@Icons.Material.Filled.Security" Size="Size.Large" Color="Color.Secondary" />
                    <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mt-4">
                        @(_searchTerm.Length > 0 ? "Nenhuma role encontrada" : "Nenhuma role cadastrada")
                    </MudText>
                    @if (_searchTerm.Length == 0)
                    {
                        <MudButton Variant="Variant.Filled" Color="Color.Primary" Class="mt-4" Href="/users/roles/create">
                            Criar primeira role
                        </MudButton>
                    }
                </div>
            }
            else
            {
                <MudTable Items="_roles" Dense="true" Bordered="true" Striped="true"
                         Hover="true" 
                         Breakpoint="Breakpoint.Sm" 
                         Loading="_loading"
                         LoadingProgressColor="Color.Info">
                    <HeaderContent>
                        <MudTh>Nome</MudTh>
                        <MudTh>Descrição</MudTh>
                        <MudTh>Status</MudTh>
                        <MudTh>Criado em</MudTh>
                        <MudTh>Ações</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="Nome">
                            <div class="d-flex align-center">
                                <MudIcon Icon="@Icons.Material.Filled.Security" Color="Color.Primary" Size="Size.Small" Class="mr-2" />
                                <MudText>@context.Name</MudText>
                            </div>
                        </MudTd>
                        <MudTd DataLabel="Descrição">
                            <MudText>@(context.Description ?? "N/A")</MudText>
                        </MudTd>
                        <MudTd DataLabel="Status">
                            <MudChip T="string" Color="@(context.IsActive ? Color.Success : Color.Error)" Size="Size.Small">
                                @(context.IsActive ? "Ativo" : "Inativo")
                            </MudChip>
                        </MudTd>
                        <MudTd DataLabel="Criado em">
                            @context.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                        </MudTd>
                        <MudTd DataLabel="Ações">
                            <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                             Color="Color.Primary"
                                             Size="Size.Small"
                                             Href="@($"/users/roles/edit/{context.Id}")" />
                                <MudIconButton Icon="@(context.IsActive ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)"
                                             Color="@(context.IsActive ? Color.Warning : Color.Success)"
                                             Size="Size.Small"
                                             OnClick="@(() => ToggleRoleStatus(context))" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                             Color="Color.Error"
                                             Size="Size.Small"
                                             OnClick="@(() => DeleteRole(context))" />
                            </MudButtonGroup>
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            }
        </MudCardContent>
    </MudCard>

    <!-- Paginação -->
    @if (_pagedResult.TotalCount > 0)
    {
        <div class="d-flex justify-center mt-4">
            <MudPagination Count="@((_pagedResult.TotalCount + PageSize - 1) / PageSize)" 
                          Selected="_currentPage" 
                          SelectedChanged="OnPageChanged" />
        </div>
    }
</MudContainer>

@code {
    private List<ApplicationRole> _roles = new();
    private PagedResult<ApplicationRole> _pagedResult = new();
    private bool _loading = true;
    private string _searchTerm = string.Empty;
    private bool? _selectedStatus;
    private int _currentPage = 1;
    private const int PageSize = 10;

    protected override async Task OnInitializedAsync()
    {
        await LoadRoles();
    }

    private async Task LoadRoles()
    {
        _loading = true;
        try
        {
            _pagedResult = await RoleService.GetRolesPagedAsync(_currentPage, PageSize, _searchTerm, _selectedStatus);
            _roles = _pagedResult.Items.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar roles: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task SearchRoles()
    {
        _currentPage = 1;
        await LoadRoles();
    }

    private async Task OnPageChanged(int page)
    {
        _currentPage = page;
        await LoadRoles();
    }

    private async Task ToggleRoleStatus(ApplicationRole role)
    {
        try
        {
            var result = await RoleService.ToggleRoleStatusAsync(role.Id);
            if (result.Success)
            {
                Snackbar.Add(result.Message ?? "Status alterado com sucesso!", Severity.Success);
                await LoadRoles();
            }
            else
            {
                Snackbar.Add(result.Message ?? "Erro ao alterar status", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao alterar status: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteRole(ApplicationRole role)
    {
        var parameters = new DialogParameters<ConfirmDialog>
        {
            { x => x.ContentText, $"Tem certeza que deseja excluir a role '{role.Name}'? Esta ação não pode ser desfeita." },
            { x => x.ButtonText, "Excluir" },
            { x => x.Color, Color.Error }
        };

        var dialog = await DialogService.ShowAsync<ConfirmDialog>("Confirmar Exclusão", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            try
            {
                var deleteResult = await RoleService.DeleteRoleAsync(role.Id);
                if (deleteResult.Success)
                {
                    Snackbar.Add(deleteResult.Message ?? "Role excluída com sucesso!", Severity.Success);
                    await LoadRoles();
                }
                else
                {
                    Snackbar.Add(deleteResult.Message ?? "Erro ao excluir role", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Erro ao excluir role: {ex.Message}", Severity.Error);
            }
        }
    }
}
