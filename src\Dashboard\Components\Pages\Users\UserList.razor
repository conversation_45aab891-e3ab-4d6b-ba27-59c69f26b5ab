@page "/users"
@inherits AdminPageBase
@inject IUserManagementService UserService
@inject IDialogService DialogService
@inject ISnackbar Snackbar
@using AssetView.Nx.Modules.Users.Services
@using AssetView.Nx.Core.Entities
@using AssetView.Nx.Dashboard.Components.Shared

<PageTitle>Usuários - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">Usuários</MudText>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.PersonAdd" Href="/users/create">
            Novo Usuário
        </MudButton>
    </div>

    <MudCard Elevation="2">
        <MudCardContent>
            <div class="d-flex gap-4 mb-4">
                <MudTextField @bind-Value="_searchTerm" 
                             Label="Buscar usuários" 
                             Variant="Variant.Outlined" 
                             Adornment="Adornment.End" 
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchUsers(); })" />
                
                <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="SearchUsers">
                    Buscar
                </MudButton>
            </div>

            @if (_loading)
            {
                <div class="d-flex justify-center pa-4">
                    <MudProgressCircular Indeterminate="true" />
                </div>
            }
            else
            {
                <MudTable Items="_users" 
                         Hover="true" 
                         Breakpoint="Breakpoint.Sm" 
                         Loading="_loading"
                         LoadingProgressColor="Color.Info">
                    <HeaderContent>
                        <MudTh>Nome</MudTh>
                        <MudTh>Email</MudTh>
                        <MudTh>Usuário</MudTh>
                        <MudTh>Telefone</MudTh>
                        <MudTh>Status</MudTh>
                        <MudTh>Email Confirmado</MudTh>
                        <MudTh>Último Login</MudTh>
                        <MudTh>Ações</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="Nome">
                            <div class="d-flex align-center">
                                <MudAvatar Color="Color.Primary" Size="Size.Small">
                                    @context.FullName.Substring(0, 1).ToUpper()
                                </MudAvatar>
                                <MudText Class="ml-2">@context.FullName</MudText>
                            </div>
                        </MudTd>
                        <MudTd DataLabel="Email">@context.Email</MudTd>
                        <MudTd DataLabel="Usuário">@context.UserName</MudTd>
                        <MudTd DataLabel="Telefone">@(context.PhoneNumber ?? "N/A")</MudTd>
                        <MudTd DataLabel="Status">
                            <MudChip T="string" Color="@(context.IsActive ? Color.Success : Color.Error)" Size="Size.Small">
                                @(context.IsActive ? "Ativo" : "Inativo")
                            </MudChip>
                        </MudTd>
                        <MudTd DataLabel="Email Confirmado">
                            <MudIcon Icon="@(context.EmailConfirmed ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)" 
                                    Color="@(context.EmailConfirmed ? Color.Success : Color.Error)" />
                        </MudTd>
                        <MudTd DataLabel="Último Login">
                            @(context.LastLoginAt?.ToString("dd/MM/yyyy HH:mm") ?? "Nunca")
                        </MudTd>
                        <MudTd DataLabel="Ações">
                            <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                             Color="Color.Primary" 
                                             Size="Size.Small"
                                             Href="@($"/users/edit/{context.Id}")" />
                                <MudIconButton Icon="@(context.IsActive ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)" 
                                             Color="@(context.IsActive ? Color.Warning : Color.Success)" 
                                             Size="Size.Small"
                                             OnClick="@(() => ToggleUserStatus(context))" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                             Color="Color.Error" 
                                             Size="Size.Small"
                                             OnClick="@(() => DeleteUser(context))" />
                            </MudButtonGroup>
                        </MudTd>
                    </RowTemplate>
                    <NoRecordsContent>
                        <MudText Typo="Typo.body1" Align="Align.Center" Class="pa-4">
                            Nenhum usuário encontrado
                        </MudText>
                    </NoRecordsContent>
                </MudTable>

                @if (_pagedResult.TotalPages > 1)
                {
                    <div class="d-flex justify-center mt-4">
                        <MudPagination Count="_pagedResult.TotalPages" 
                                     Selected="_currentPage" 
                                     SelectedChanged="OnPageChanged" 
                                     ShowFirstButton="true" 
                                     ShowLastButton="true" />
                    </div>
                }
            }
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private List<ApplicationUser> _users = new();
    private PagedResult<ApplicationUser> _pagedResult = new();
    private bool _loading = true;
    private string _searchTerm = string.Empty;
    private int _currentPage = 1;
    private const int PageSize = 10;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        _loading = true;
        try
        {
            _pagedResult = await UserService.GetUsersPagedAsync(_currentPage, PageSize, _searchTerm);
            _users = _pagedResult.Items.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar usuários: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task SearchUsers()
    {
        _currentPage = 1;
        await LoadUsers();
    }

    private async Task OnPageChanged(int page)
    {
        _currentPage = page;
        await LoadUsers();
    }

    private async Task ToggleUserStatus(ApplicationUser user)
    {
        var action = user.IsActive ? "desativar" : "ativar";
        var parameters = new DialogParameters
        {
            ["ContentText"] = $"Tem certeza que deseja {action} o usuário '{user.FullName}'?",
            ["ButtonText"] = action.Substring(0, 1).ToUpper() + action.Substring(1),
            ["Color"] = user.IsActive ? Color.Warning : Color.Success
        };

        var dialog = await DialogService.ShowAsync<ConfirmDialog>($"Confirmar {action.Substring(0, 1).ToUpper() + action.Substring(1)}", parameters);
        var result = await dialog.Result;

        if (result != null && !result.Canceled)
        {
            try
            {
                var toggleResult = await UserService.ToggleUserStatusAsync(user.Id);
                if (toggleResult.Success)
                {
                    Snackbar.Add($"Usuário {action}do com sucesso", Severity.Success);
                    await LoadUsers();
                }
                else
                {
                    Snackbar.Add(toggleResult.Message ?? $"Erro ao {action} usuário", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Erro ao {action} usuário: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task DeleteUser(ApplicationUser user)
    {
        var parameters = new DialogParameters
        {
            ["ContentText"] = $"Tem certeza que deseja excluir o usuário '{user.FullName}'? Esta ação não pode ser desfeita.",
            ["ButtonText"] = "Excluir",
            ["Color"] = Color.Error
        };

        var dialog = await DialogService.ShowAsync<ConfirmDialog>("Confirmar Exclusão", parameters);
        var result = await dialog.Result;

        if (result != null && !result.Canceled)
        {
            try
            {
                var deleteResult = await UserService.DeleteUserAsync(user.Id);
                if (deleteResult.Success)
                {
                    Snackbar.Add("Usuário excluído com sucesso", Severity.Success);
                    await LoadUsers();
                }
                else
                {
                    Snackbar.Add(deleteResult.Message ?? "Erro ao excluir usuário", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Erro ao excluir usuário: {ex.Message}", Severity.Error);
            }
        }
    }
}
