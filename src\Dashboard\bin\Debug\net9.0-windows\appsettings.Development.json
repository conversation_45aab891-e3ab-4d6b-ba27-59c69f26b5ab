{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.SignalR": "Debug", "AssetView.Nx.Dashboard.Services": "Debug", "AssetView.Nx.Modules.OpcDa": "Debug"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.SignalR": "Debug", "AssetView.Nx.Dashboard.Services": "Debug", "AssetView.Nx.Modules.OpcDa": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:HH:mm:ss.fff} [{Level:u3}] [{SourceContext:l}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/blazor-dashboard-dev-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "fileSizeLimitBytes": 52428800, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}]}, "EmailSettings": {"Enabled": true, "SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "EnableSsl": true, "UserName": "<EMAIL>", "Password": "your-app-password", "FromEmail": "<EMAIL>", "FromName": "Smar Hart System - DEV", "ReplyToEmail": "<EMAIL>", "TimeoutSeconds": 30, "BaseUrl": "https://*:7129"}}