is_global = true
build_property.MudDebugAnalyzer = 
build_property.MudAllowedAttributePattern = 
build_property.MudAllowedAttributeList = 
build_property.TargetFramework = net9.0-windows
build_property.TargetFramework = net9.0-windows
build_property.TargetPlatformMinVersion = 7.0
build_property.TargetPlatformMinVersion = 7.0
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = AssetView.Nx.Dashboard
build_property.RootNamespace = AssetView.Nx.Dashboard
build_property.ProjectDir = C:\Users\<USER>\Documents\Smar.AssetView.Nx\src\Dashboard\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.CsWinRTUseWindowsUIXamlProjections = false
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\Documents\Smar.AssetView.Nx\src\Dashboard
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/App.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Layout/AuthLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcQXV0aExheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTWFpbkxheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Layout/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTmF2TWVudS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/AccessDenied.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBY2Nlc3NEZW5pZWQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Auth/ForgotPassword.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBdXRoXEZvcmdvdFBhc3N3b3JkLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Auth/ForgotPasswordConfirmation.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBdXRoXEZvcmdvdFBhc3N3b3JkQ29uZmlybWF0aW9uLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Auth/Login.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBdXRoXExvZ2luLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Auth/Logout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBdXRoXExvZ291dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Auth/Register.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBdXRoXFJlZ2lzdGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Auth/ResetPassword.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBdXRoXFJlc2V0UGFzc3dvcmQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Counter.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xDb3VudGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Debug.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEZWJ1Zy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Error.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFcnJvci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Home.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/OPC/Dialogs/AddTagDialog.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xPUENcRGlhbG9nc1xBZGRUYWdEaWFsb2cucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/OPC/Dialogs/CreateGroupDialog.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xPUENcRGlhbG9nc1xDcmVhdGVHcm91cERpYWxvZy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/OPC/NodeListItem.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xPUENcTm9kZUxpc3RJdGVtLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/OPC/OpcDashboard.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xPUENcT3BjRGFzaGJvYXJkLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/OPC/OpcMonitor.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xPUENcT3BjTW9uaXRvci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/OPC/OpcServerManager.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xPUENcT3BjU2VydmVyTWFuYWdlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/OPC/OpcTagBrowser.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xPUENcT3BjVGFnQnJvd3Nlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Profile/UserProfile.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xQcm9maWxlXFVzZXJQcm9maWxlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Projects/ProjectForm.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xQcm9qZWN0c1xQcm9qZWN0Rm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Projects/ProjectList.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xQcm9qZWN0c1xQcm9qZWN0TGlzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Settings/Settings.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xTZXR0aW5nc1xTZXR0aW5ncy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Users/<USER>
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xVc2Vyc1xSb2xlRm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Users/<USER>
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xVc2Vyc1xSb2xlTGlzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Users/<USER>
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xVc2Vyc1xVc2VyRm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Users/<USER>
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xVc2Vyc1xVc2VyTGlzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Pages/Weather.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXZWF0aGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Routes.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSb3V0ZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Shared/ConfirmDialog.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaGFyZWRcQ29uZmlybURpYWxvZy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/Shared/RedirectToLogin.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaGFyZWRcUmVkaXJlY3RUb0xvZ2luLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/Documents/Smar.AssetView.Nx/src/Dashboard/Components/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xfSW1wb3J0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 
