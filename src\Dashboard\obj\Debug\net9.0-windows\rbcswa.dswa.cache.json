{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["WjUw0b//qyHCUS13+dfAr3t+6CaE2GkynTmf1vSWxOk=", "R76rm2ITHnglPf+XBVzY8uYQ3MJhvSvaPNlM459du9E=", "ow2xatN9inDRjPLpaYup5YytxQyGmJQzcoFOk9z1Vgg=", "kTXXVum7FXiW1tWDyAkTgDMBVv1cIi+qPOSfgJQWCdI="], "CachedAssets": {"WjUw0b//qyHCUS13+dfAr3t+6CaE2GkynTmf1vSWxOk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\tzxjg6is5z-sowobu9fea.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "va5o1r7z77", "Integrity": "KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.css", "FileLength": 65109, "LastWriteTime": "2025-07-11T13:39:45.8810575+00:00"}, "R76rm2ITHnglPf+XBVzY8uYQ3MJhvSvaPNlM459du9E=": {"Identity": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\0wz98yz2xy-b8x8f7e52z.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5rcl324siu", "Integrity": "L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.9.0\\staticwebassets\\MudBlazor.min.js", "FileLength": 15475, "LastWriteTime": "2025-07-11T13:39:45.8693456+00:00"}, "ow2xatN9inDRjPLpaYup5YytxQyGmJQzcoFOk9z1Vgg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\weg7obbbjn-lp4d2hvui5.gz", "SourceId": "Extensions.MudBlazor.StaticInput", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/Extensions.MudBlazor.StaticInput", "RelativePath": "NavigationObserver.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\extensions.mudblazor.staticinput\\3.2.0\\staticwebassets\\NavigationObserver.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vis12zgog2", "Integrity": "p/TXD92fxVFbGsbb+dOA3rXexGDZOIe0KByLSYuBZ4w=", "CopyToOutputDirectory": "Always", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\extensions.mudblazor.staticinput\\3.2.0\\staticwebassets\\NavigationObserver.js", "FileLength": 1492, "LastWriteTime": "2025-07-11T13:39:45.8678644+00:00"}, "kTXXVum7FXiW1tWDyAkTgDMBVv1cIi+qPOSfgJQWCdI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\39bsjosk0g-2jeq8efc6q.gz", "SourceId": "AssetView.Nx.Dashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Dashboard\\obj\\Debug\\net9.0-windows\\compressed\\", "BasePath": "_content/AssetView.Nx.Dashboard", "RelativePath": "favicon#[.{fingerprint=2jeq8efc6q}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Dashboard\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pkzf8os278", "Integrity": "2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Dashboard\\wwwroot\\favicon.ico", "FileLength": 2920, "LastWriteTime": "2025-07-11T13:39:45.8703459+00:00"}}, "CachedCopyCandidates": {}}