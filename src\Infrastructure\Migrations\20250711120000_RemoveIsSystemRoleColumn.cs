using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AssetView.Nx.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RemoveIsSystemRoleColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Remover coluna IsSystemRole da tabela Roles
            migrationBuilder.DropColumn(
                name: "IsSystemRole",
                table: "Roles");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Adicionar coluna IsSystemRole de volta na tabela Roles
            migrationBuilder.AddColumn<bool>(
                name: "IsSystemRole",
                table: "Roles",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);
        }
    }
}
