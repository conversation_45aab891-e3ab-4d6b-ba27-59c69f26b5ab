using AssetView.Nx.Core.Entities;
using AssetView.Nx.Core.Interfaces;
using AssetView.Nx.Shared.Models;
using AssetView.Nx.Infrastructure.Repositories;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AssetView.Nx.Modules.Projects.Services;


/// <summary>
/// Implementação do serviço de projetos
/// </summary>
public class ProjectService : IProjectService
{
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<ApplicationUser> _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ProjectService> _logger;

    public ProjectService(
        IRepository<Project> projectRepository,
        IRepository<ApplicationUser> userRepository,
        IUnitOfWork unitOfWork,
        ILogger<ProjectService> logger)
    {
        _projectRepository = projectRepository;
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<IEnumerable<Project>> GetUserProjectsAsync(string userId)
    {
        return await _projectRepository.GetAsync(p => p.OwnerId == userId);
    }

    public async Task<PagedResult<Project>> GetProjectsPagedAsync(int page, int pageSize, string? searchTerm = null, ProjectStatus? status = null)
    {
        try
        {
            var (projects, totalCount) = await _projectRepository.GetPagedAsync(
                page,
                pageSize,
                predicate: p => 
                    (string.IsNullOrEmpty(searchTerm) || p.Name.Contains(searchTerm) || (p.Description != null && p.Description.Contains(searchTerm))) &&
                    (!status.HasValue || p.Status == status.Value),
                orderBy: p => p.CreatedAt,
                ascending: false
            );

            return new PagedResult<Project>
            {
                Items = projects,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter projetos paginados");
            return new PagedResult<Project>();
        }
    }

    public async Task<Project?> GetProjectByIdAsync(string projectId)
    {
        return await _projectRepository.GetByIdAsync(projectId);
    }

    public async Task<ProjectResult> CreateProjectAsync(CreateProjectRequest request)
    {
        try
        {
            // Validar se o owner existe - primeiro tentar no tenant atual
            var owner = await _userRepository.GetByIdAsync(request.OwnerId);
            if (owner == null)
            {
                // Se não encontrou no tenant atual, tentar buscar sem filtro de tenant
                try
                {
                    var baseRepo = (Repository<ApplicationUser>)_userRepository;
                    owner = await baseRepo.GetByIdAsync(request.OwnerId);
                    
                    if (owner != null)
                    {
                        // Atualizar o tenant do usuário para o atual se necessário
                        if (owner.TenantId != currentTenantId && !string.IsNullOrEmpty(currentTenantId))
                        {
                            owner.TenantId = currentTenantId;
                            await _userRepository.UpdateAsync(owner);
                            await _unitOfWork.SaveChangesAsync();
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro ao buscar usuário sem filtro de tenant");
                }
            }
            // Validar datas
            if (request.StartDate.HasValue && request.EndDate.HasValue && request.StartDate > request.EndDate)
            {
                return new ProjectResult
                {
                    Success = false,
                    Message = "Data de início não pode ser posterior à data de fim"
                };
            }

            var project = new Project
            {
                Name = request.Name,
                Description = request.Description,
                OwnerId = request.OwnerId,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                Status = request.Status,
                Priority = request.Priority,
                Budget = request.Budget,
                Progress = 0,
                Tags = request.Tags.Any() ? JsonSerializer.Serialize(request.Tags) : null,
                IsActive = true
            };

            await _projectRepository.AddAsync(project);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Projeto {ProjectName} criado com sucesso", request.Name);

            return new ProjectResult
            {
                Success = true,
                Message = "Projeto criado com sucesso",
                Project = project
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao criar projeto {ProjectName}", request.Name);
            return new ProjectResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<ProjectResult> UpdateProjectAsync(string projectId, UpdateProjectRequest request)
    {
        try
        {
            var project = await _projectRepository.GetByIdAsync(projectId);
            if (project == null)
            {
                return new ProjectResult
                {
                    Success = false,
                    Message = "Projeto não encontrado"
                };
            }

            // Validar datas
            if (request.StartDate.HasValue && request.EndDate.HasValue && request.StartDate > request.EndDate)
            {
                return new ProjectResult
                {
                    Success = false,
                    Message = "Data de início não pode ser posterior à data de fim"
                };
            }

            project.Name = request.Name;
            project.Description = request.Description;
            project.StartDate = request.StartDate;
            project.EndDate = request.EndDate;
            project.Priority = request.Priority;
            project.Budget = request.Budget;
            project.Tags = request.Tags.Any() ? JsonSerializer.Serialize(request.Tags) : null;

            await _projectRepository.UpdateAsync(project);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Projeto {ProjectId} atualizado com sucesso", projectId);

            return new ProjectResult
            {
                Success = true,
                Message = "Projeto atualizado com sucesso",
                Project = project
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar projeto {ProjectId}", projectId);
            return new ProjectResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<ProjectResult> UpdateProjectStatusAsync(string projectId, ProjectStatus status)
    {
        try
        {
            var project = await _projectRepository.GetByIdAsync(projectId);
            if (project == null)
            {
                return new ProjectResult
                {
                    Success = false,
                    Message = "Projeto não encontrado"
                };
            }

            project.Status = status;

            // Atualizar progresso automaticamente baseado no status
            switch (status)
            {
                case ProjectStatus.Planning:
                    project.Progress = 0;
                    break;
                case ProjectStatus.InProgress:
                    if (project.Progress == 0) project.Progress = 10;
                    break;
                case ProjectStatus.Completed:
                    project.Progress = 100;
                    break;
                case ProjectStatus.Cancelled:
                    // Manter progresso atual
                    break;
            }

            await _projectRepository.UpdateAsync(project);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Status do projeto {ProjectId} atualizado para {Status}", projectId, status);

            return new ProjectResult
            {
                Success = true,
                Message = "Status do projeto atualizado com sucesso",
                Project = project
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar status do projeto {ProjectId}", projectId);
            return new ProjectResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<ProjectResult> UpdateProjectProgressAsync(string projectId, int progress)
    {
        try
        {
            if (progress < 0 || progress > 100)
            {
                return new ProjectResult
                {
                    Success = false,
                    Message = "Progresso deve estar entre 0 e 100"
                };
            }

            var project = await _projectRepository.GetByIdAsync(projectId);
            if (project == null)
            {
                return new ProjectResult
                {
                    Success = false,
                    Message = "Projeto não encontrado"
                };
            }

            project.Progress = progress;

            // Atualizar status automaticamente baseado no progresso
            if (progress == 0)
            {
                project.Status = ProjectStatus.Planning;
            }
            else if (progress == 100)
            {
                project.Status = ProjectStatus.Completed;
            }
            else if (project.Status == ProjectStatus.Planning || project.Status == ProjectStatus.Completed)
            {
                project.Status = ProjectStatus.InProgress;
            }

            await _projectRepository.UpdateAsync(project);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Progresso do projeto {ProjectId} atualizado para {Progress}%", projectId, progress);

            return new ProjectResult
            {
                Success = true,
                Message = "Progresso do projeto atualizado com sucesso",
                Project = project
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar progresso do projeto {ProjectId}", projectId);
            return new ProjectResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<ProjectResult> DeleteProjectAsync(string projectId)
    {
        try
        {
            var project = await _projectRepository.GetByIdAsync(projectId);
            if (project == null)
            {
                return new ProjectResult
                {
                    Success = false,
                    Message = "Projeto não encontrado"
                };
            }

            project.IsDeleted = true;
            project.DeletedAt = DateTime.UtcNow;
            await _projectRepository.UpdateAsync(project);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Projeto {ProjectId} excluído com sucesso", projectId);

            return new ProjectResult
            {
                Success = true,
                Message = "Projeto excluído com sucesso"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao excluir projeto {ProjectId}", projectId);
            return new ProjectResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<ProjectStatistics> GetProjectStatisticsAsync(string? userId = null)
    {
        try
        {
            var projects = userId != null 
                ? await _projectRepository.GetAsync(p => p.OwnerId == userId)
                : await _projectRepository.GetAllAsync();

            var projectList = projects.ToList();

            return new ProjectStatistics
            {
                TotalProjects = projectList.Count,
                ActiveProjects = projectList.Count(p => p.Status == ProjectStatus.InProgress),
                CompletedProjects = projectList.Count(p => p.Status == ProjectStatus.Completed),
                OnHoldProjects = projectList.Count(p => p.Status == ProjectStatus.OnHold),
                CancelledProjects = projectList.Count(p => p.Status == ProjectStatus.Cancelled),
                TotalBudget = projectList.Where(p => p.Budget.HasValue).Sum(p => p.Budget!.Value),
                AverageProgress = projectList.Any() ? projectList.Average(p => p.Progress) : 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas dos projetos");
            return new ProjectStatistics();
        }
    }
}
